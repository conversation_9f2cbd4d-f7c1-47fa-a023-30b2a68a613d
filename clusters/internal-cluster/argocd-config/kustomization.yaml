apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: argocd-config
  annotations:
    argocd.argoproj.io/sync-options: SkipDryRunOnMissingResource=true

resources:
  - github.com/argoproj/argo-cd//manifests/cluster-install?ref=stable
  - gateway-integration/argocd-ssl-certificate.yaml
  - gateway-integration/argocd-reference-grant.yaml
  - gateway-integration/argocd-gateway.yaml
  - gateway-integration/argocd-https.yaml
  - gateway-integration/argocd-http2https-redirect.yaml
  - core/argocd-server-config.yaml
