apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: argocd-eg
  namespace: default
  annotations:
    argocd.argoproj.io/sync-wave: "4"
spec:
  gatewayClassName: merged-eg
  listeners:
    - name: http
      port: 80
      protocol: HTTP
      hostname: argocd.wittig.nl
      allowedRoutes:
        namespaces:
          from: All
    - name: https
      port: 443
      protocol: HTTPS
      hostname: argocd.wittig.nl
      allowedRoutes:
        namespaces:
          from: All
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: argocd-tls-cert
            namespace: default
