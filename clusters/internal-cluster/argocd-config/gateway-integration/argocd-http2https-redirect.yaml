apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-http2https-redirect
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "5"
spec:
  parentRefs:
    - kind: Gateway
      name: argocd-eg
      namespace: default
      sectionName: http
  hostnames:
    - argocd.wittig.nl
  rules:
    - filters:
        - type: RequestRedirect
          requestRedirect:
            scheme: https
            statusCode: 301
