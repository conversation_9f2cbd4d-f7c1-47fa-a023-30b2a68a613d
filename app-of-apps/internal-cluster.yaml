apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: internal-cluster
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: "https://github.com/wittignl/k8s-clusters"
    path: "clusters/internal-cluster"
    targetRevision: HEAD
  destination:
    server: "https://kubernetes.default.svc"
    namespace: argocd
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
