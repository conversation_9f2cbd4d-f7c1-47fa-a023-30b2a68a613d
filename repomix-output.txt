This file is a merged representation of the entire codebase, combined into a single document by Repomix.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded

Additional Info:
----------------

================================================================
Directory Structure
================================================================
app-of-apps/
  internal-cluster.yaml
base/
  infrastructure/
    cert-manager/
      templates/
        cert-manager-challenge-gateway.yaml
        issuer-letsencrypt-production.yaml
        issuer-letsencrypt-staging.yaml
      Chart.yaml
      values.yaml
    envoy-gateway/
      templates/
        custom-proxy-config.yaml
        merged-eg.yaml
      Chart.yaml
      values.yaml
clusters/
  internal-cluster/
    argocd-config/
      core/
        argocd-server-config.yaml
      gateway-integration/
        argocd-gateway.yaml
        argocd-http2https-redirect.yaml
        argocd-https.yaml
        argocd-reference-grant.yaml
        argocd-ssl-certificate.yaml
      kustomization.yaml
    argocd-app.yaml
    cert-manager-app.yaml
    envoy-gateway-app.yaml
    prometheus-operator-app.yaml

================================================================
Files
================================================================

================
File: app-of-apps/internal-cluster.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: internal-cluster
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: "https://github.com/wittignl/k8s-clusters"
    path: "clusters/internal-cluster"
    targetRevision: HEAD
  destination:
    server: "https://kubernetes.default.svc"
    namespace: argocd
  syncPolicy:
    automated:
      prune: true
      selfHeal: true

================
File: base/infrastructure/cert-manager/templates/cert-manager-challenge-gateway.yaml
================
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: cert-manager-eg
  namespace: default
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  gatewayClassName: merged-eg
  listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP

================
File: base/infrastructure/cert-manager/templates/issuer-letsencrypt-production.yaml
================
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: issuer-letsencrypt-production
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  acme:
    email: <EMAIL>
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: issuer-letsencrypt-production
    solvers:
      - http01:
          gatewayHTTPRoute:
            parentRefs:
              - kind: Gateway
                name: cert-manager-eg
                namespace: default

================
File: base/infrastructure/cert-manager/templates/issuer-letsencrypt-staging.yaml
================
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: issuer-letsencrypt-staging
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  acme:
    email: <EMAIL>
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: issuer-letsencrypt-staging
    solvers:
      - http01:
          gatewayHTTPRoute:
            parentRefs:
              - kind: Gateway
                name: cert-manager-eg
                namespace: default

================
File: base/infrastructure/cert-manager/Chart.yaml
================
apiVersion: v2
name: cert-manager-umbrella
description: An umbrella chart for deploying cert-manager with custom configurations.
type: application
version: 0.1.0
appVersion: "v1.5.0"

dependencies:
  - name: cert-manager
    version: "1.5.0"
    repository: "oci://registry-1.docker.io/bitnamicharts"

================
File: base/infrastructure/cert-manager/values.yaml
================
# Cert-Manager Configuration
cert-manager:
  installCRDs: true

  metrics:
    enabled: true
    serviceMonitor:
      enabled: true
      namespace: "monitoring"

  controller:
    extraArgs:
      - --feature-gates=ExperimentalGatewayAPISupport=true
      - --enable-gateway-api

================
File: base/infrastructure/envoy-gateway/templates/custom-proxy-config.yaml
================
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: custom-proxy-config
  namespace: envoy-gateway-system
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  mergeGateways: true

================
File: base/infrastructure/envoy-gateway/templates/merged-eg.yaml
================
apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: merged-eg
  namespace: default
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
  parametersRef:
    group: gateway.envoyproxy.io
    kind: EnvoyProxy
    name: custom-proxy-config
    namespace: envoy-gateway-system

================
File: base/infrastructure/envoy-gateway/Chart.yaml
================
apiVersion: v2
name: envoy-gateway-umbrella
description: An umbrella chart for deploying Envoy Gateway and custom configurations.
type: application
version: 0.1.0
appVersion: "v1.4.1"

dependencies:
  - name: gateway-helm
    version: "v1.4.1"
    repository: "oci://docker.io/envoyproxy"

================
File: base/infrastructure/envoy-gateway/values.yaml
================
gateway-helm:
  envoyGateway:
    gateway:
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
    provider:
      type: Kubernetes
    mergeGateways: true

================
File: clusters/internal-cluster/argocd-config/core/argocd-server-config.yaml
================
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-server-config
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-server-config
    app.kubernetes.io/part-of: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "1"
data:
  url: https://argocd.wittig.nl

================
File: clusters/internal-cluster/argocd-config/gateway-integration/argocd-gateway.yaml
================
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: argocd-eg
  namespace: default
  annotations:
    argocd.argoproj.io/sync-wave: "4"
spec:
  gatewayClassName: merged-eg
  listeners:
    - name: http
      port: 80
      protocol: HTTP
      hostname: argocd.wittig.nl
      allowedRoutes:
        namespaces:
          from: All
    - name: https
      port: 443
      protocol: HTTPS
      hostname: argocd.wittig.nl
      allowedRoutes:
        namespaces:
          from: All
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: argocd-tls-cert
            namespace: default

================
File: clusters/internal-cluster/argocd-config/gateway-integration/argocd-http2https-redirect.yaml
================
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-http2https-redirect
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "5"
spec:
  parentRefs:
    - kind: Gateway
      name: argocd-eg
      namespace: default
      sectionName: http
  hostnames:
    - argocd.wittig.nl
  rules:
    - filters:
        - type: RequestRedirect
          requestRedirect:
            scheme: https
            statusCode: 301

================
File: clusters/internal-cluster/argocd-config/gateway-integration/argocd-https.yaml
================
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: argocd-https
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "5"
spec:
  parentRefs:
    - kind: Gateway
      name: argocd-eg
      namespace: default
      sectionName: https
  hostnames:
    - argocd.wittig.nl
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - kind: Service
          name: argocd-server
          namespace: argocd
          port: 80

================
File: clusters/internal-cluster/argocd-config/gateway-integration/argocd-reference-grant.yaml
================
apiVersion: gateway.networking.k8s.io/v1beta1
kind: ReferenceGrant
metadata:
  name: allow-gateway-to-reference-argocd-tls
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  from:
    - group: gateway.networking.k8s.io
      kind: Gateway
      namespace: default
  to:
    - group: ""
      kind: Secret
      name: argocd-tls-cert

================
File: clusters/internal-cluster/argocd-config/gateway-integration/argocd-ssl-certificate.yaml
================
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: argocd-tls-cert
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  secretName: argocd-tls
  issuerRef:
    name: issuer-letsencrypt-production
    kind: ClusterIssuer
  dnsNames:
    - argocd.wittig.nl

================
File: clusters/internal-cluster/argocd-config/kustomization.yaml
================
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: argocd-config
  annotations:
    argocd.argoproj.io/sync-options: SkipDryRunOnMissingResource=true

resources:
  - github.com/argoproj/argo-cd//manifests/cluster-install?ref=stable
  - gateway-integration/argocd-ssl-certificate.yaml
  - gateway-integration/argocd-reference-grant.yaml
  - gateway-integration/argocd-gateway.yaml
  - gateway-integration/argocd-https.yaml
  - gateway-integration/argocd-http2https-redirect.yaml
  - core/argocd-server-config.yaml

================
File: clusters/internal-cluster/argocd-app.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: argocd-config
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "10"
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/wittignl/k8s-clusters
    path: clusters/internal-cluster/argocd
    targetRevision: HEAD
  destination:
    server: https://kubernetes.default.svc
    namespace: default
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true

================
File: clusters/internal-cluster/cert-manager-app.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cert-manager
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  source:
    repoURL: https://github.com/wittignl/k8s-clusters
    path: base/infrastructure/cert-manager
    targetRevision: HEAD
    helm:
      releaseName: cert-manager
  destination:
    server: https://kubernetes.default.svc
    namespace: cert-manager
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true

================
File: clusters/internal-cluster/envoy-gateway-app.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: envoy-gateway
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  source:
    repoURL: https://github.com/wittignl/k8s-clusters
    path: base/infrastructure/envoy-gateway
    targetRevision: HEAD
    helm:
      releaseName: eg
  destination:
    server: https://kubernetes.default.svc
    namespace: envoy-gateway-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true

================
File: clusters/internal-cluster/prometheus-operator-app.yaml
================
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: prometheus-operator
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: infra
  source:
    repoURL: registry-1.docker.io/bitnamicharts
    chart: kube-prometheus
    targetRevision: 11.1.17
    helm:
      releaseName: prometheus-operator
  destination:
    server: https://kubernetes.default.svc
    namespace: monitoring
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - ServerSideApply=true



================================================================
End of Codebase
================================================================
